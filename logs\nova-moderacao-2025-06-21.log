[21/06/2025 23:30:07] INFO: ▣ Sistema de logs inicializado
[21/06/2025 23:30:07] INFO: ▣ Gerenciador de configurações inicializado
[21/06/2025 23:30:08] INFO: ▣ Banco de dados inicializado
[21/06/2025 23:30:08] INFO: ▣ 28 comandos carregados
[21/06/2025 23:30:08] INFO: ▣ Gerenciador de estados de comandos inicializado
[21/06/2025 23:30:08] INFO: ▣ Eventos carregados
[21/06/2025 23:30:08] INFO: 🛡️ Sistema Anti-Raid inicializado
[21/06/2025 23:30:08] INFO: 🤖 Sistema de Auto-Moderação inicializado
[21/06/2025 23:30:08] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[21/06/2025 23:30:08] INFO: ▣ Sistemas de moderação inicializados
[21/06/2025 23:30:08] INFO: ▣ Sistema de Analytics de Moderação inicializado
[21/06/2025 23:30:08] INFO: ▣ Sistema de Backup inicializado
[21/06/2025 23:30:08] INFO: ▣ Inicializando Sistema de Verificação...
[21/06/2025 23:30:08] INFO: ▣ Sistema de Verificação inicializado com sucesso
[21/06/2025 23:30:08] INFO: ▣ Sistema de Verificação inicializado
[21/06/2025 23:30:08] INFO: ▣ Servidor web inicializado
[21/06/2025 23:35:00] INFO: ▣ Sistema de logs inicializado
[21/06/2025 23:35:00] INFO: ▣ Gerenciador de configurações inicializado
[21/06/2025 23:35:00] INFO: ▣ Banco de dados inicializado
[21/06/2025 23:35:00] INFO: ▣ 28 comandos carregados
[21/06/2025 23:35:00] INFO: ▣ Gerenciador de estados de comandos inicializado
[21/06/2025 23:35:00] INFO: ▣ Eventos carregados
[21/06/2025 23:35:00] INFO: 🛡️ Sistema Anti-Raid inicializado
[21/06/2025 23:35:00] INFO: 🤖 Sistema de Auto-Moderação inicializado
[21/06/2025 23:35:00] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[21/06/2025 23:35:00] INFO: ▣ Sistemas de moderação inicializados
[21/06/2025 23:35:00] INFO: ▣ Sistema de Analytics de Moderação inicializado
[21/06/2025 23:35:00] INFO: ▣ Sistema de Backup inicializado
[21/06/2025 23:35:00] INFO: ▣ Inicializando Sistema de Verificação...
[21/06/2025 23:35:00] INFO: ▣ Sistema de Verificação inicializado com sucesso
[21/06/2025 23:35:00] INFO: ▣ Sistema de Verificação inicializado
[21/06/2025 23:35:00] INFO: ▣ Servidor web inicializado
[21/06/2025 23:35:51] INFO: ▣ Sistema de logs inicializado
[21/06/2025 23:35:51] INFO: ▣ Gerenciador de configurações inicializado
[21/06/2025 23:35:51] INFO: ▣ Banco de dados inicializado
[21/06/2025 23:35:52] INFO: ▣ 28 comandos carregados
[21/06/2025 23:35:52] INFO: ▣ Gerenciador de estados de comandos inicializado
[21/06/2025 23:35:52] INFO: ▣ Eventos carregados
[21/06/2025 23:35:52] INFO: 🛡️ Sistema Anti-Raid inicializado
[21/06/2025 23:35:52] INFO: 🤖 Sistema de Auto-Moderação inicializado
[21/06/2025 23:35:52] INFO: 🧠 Sistema de IA para moderação inicializado (openrouter - deepseek/deepseek-chat-v3-0324:free)
[21/06/2025 23:35:52] INFO: ▣ Sistemas de moderação inicializados
[21/06/2025 23:35:52] INFO: ▣ Sistema de Analytics de Moderação inicializado
[21/06/2025 23:35:52] INFO: ▣ Sistema de Backup inicializado
[21/06/2025 23:35:52] INFO: ▣ Inicializando Sistema de Verificação...
[21/06/2025 23:35:52] INFO: ▣ Sistema de Verificação inicializado com sucesso
[21/06/2025 23:35:52] INFO: ▣ Sistema de Verificação inicializado
[21/06/2025 23:35:52] INFO: ▣ Servidor web inicializado
[21/06/2025 23:35:54] INFO: 🔄 Registrando comandos slash...
[21/06/2025 23:35:54] INFO: ✅ 28 comandos slash registrados globalmente
[21/06/2025 23:35:54] INFO: 🔍 Verificando configurações dos servidores...
[21/06/2025 23:35:54] INFO: ✅ Verificação concluída: 3 configurados, 0 novos
[21/06/2025 23:35:54] INFO: 📋 Cache de configurações carregado para 3 servidores
[21/06/2025 23:35:54] INFO: ✅ Tarefas periódicas inicializadas
[21/06/2025 23:35:54] INFO: STARTUP: Bot - success | Meta: {"component":"Bot","status":"success","guilds":3,"users":31,"commands":28,"uptime":5.1699411}
[21/06/2025 23:40:06] INFO: COMANDO: help executado | Meta: {"command":"help","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[21/06/2025 23:40:17] INFO: COMANDO: warn executado | Meta: {"command":"warn","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[21/06/2025 23:40:40] INFO: Usuário nodex_dev advertido por emp2866: cu (Advertência #2)
[21/06/2025 23:40:40] INFO: COMANDO: warn executado | Meta: {"command":"warn","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[21/06/2025 23:40:48] INFO: COMANDO: warnings executado | Meta: {"command":"warnings","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[21/06/2025 23:42:45] INFO: COMANDO: deploy-local executado | Meta: {"command":"deploy-local","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[21/06/2025 23:43:19] INFO: COMANDO: setup-panels executado | Meta: {"command":"setup-panels","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[21/06/2025 23:44:17] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1195685295568470056","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\events\\messageCreate.js:24:17)"}
[21/06/2025 23:44:29] INFO: Usuário nodex_dev advertido por emp2866: a (Advertência #2)
[21/06/2025 23:44:29] INFO: COMANDO: warn executado | Meta: {"command":"warn","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[21/06/2025 23:44:59] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1195685295568470056","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\events\\messageCreate.js:24:17)"}
[21/06/2025 23:45:16] INFO: Usuário nodex_dev advertido por emp2866: aaaa (Advertência #3)
[21/06/2025 23:45:17] INFO: COMANDO: warn executado | Meta: {"command":"warn","user":"558672715243061269","guild":"1381755403326455838","success":true,"error":null}
[21/06/2025 23:45:45] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1195685295568470056","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\events\\messageCreate.js:24:17)"}
[21/06/2025 23:46:05] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1195685295568470056","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\events\\messageCreate.js:24:17)"}
[21/06/2025 23:47:43] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1195685295568470056","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\events\\messageCreate.js:24:17)"}
[21/06/2025 23:48:12] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1195685295568470056","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\events\\messageCreate.js:24:17)"}
[21/06/2025 23:48:16] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1195685295568470056","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\events\\messageCreate.js:24:17)"}
[21/06/2025 23:48:18] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1195685295568470056","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\events\\messageCreate.js:24:17)"}
[21/06/2025 23:48:28] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1195685295568470056","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\events\\messageCreate.js:24:17)"}
[21/06/2025 23:48:39] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1195685295568470056","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\events\\messageCreate.js:24:17)"}
[21/06/2025 23:48:46] ERROR: Erro na auto-moderação: | Meta: {"guild":"1381755403326455838","user":"1195685295568470056","channel":"1382429296454537318","error":"Unexpected end of JSON input","stack":"SyntaxError: Unexpected end of JSON input\n    at JSON.parse (<anonymous>)\n    at AutoModeration.isChannelIgnored (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:642:38)\n    at AutoModeration.processMessage (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\moderation\\AutoModeration.js:86:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Object.execute (C:\\Users\\<USER>\\Desktop\\Nova pasta (42) - Copia\\events\\messageCreate.js:24:17)"}
