/**
 * ========================================
 * SISTEMA DE AUTO-MODERAÇÃO INTELIGENTE
 * Moderação automática com IA integrada
 * ========================================
 */

const { EmbedBuilder } = require('discord.js');
const Filter = require('bad-words');
const urlRegex = require('url-regex');
const sentiment = require('sentiment');
const axios = require('axios');

class AutoModeration {
    constructor(client) {
        this.client = client;
        this.filter = new Filter();
        this.sentiment = new sentiment();
        
        // Cache para análises de IA
        this.aiCache = new Map();
        
        // Rastreamento de spam/flood
        this.messageTracker = new Map();
        this.userCooldowns = new Map();
        
        // Configurações
        this.config = {
            toxicityThreshold: parseFloat(process.env.TOXICITY_THRESHOLD) || 0.7,
            spamThreshold: parseInt(process.env.SPAM_THRESHOLD) || 5,
            floodThreshold: parseInt(process.env.FLOOD_THRESHOLD) || 3,
            autoMuteEnabled: process.env.AUTO_MOD_ENABLED === 'true',
            autoMuteDuration: parseInt(process.env.AUTO_MUTE_DURATION) || 10
        };

        // Palavras personalizadas em português
        this.customBadWords = [
            // Adicionar palavrões em português aqui
            'palavrao1', 'palavrao2', // Substitua por palavras reais
        ];

        this.filter.addWords(...this.customBadWords);
        
        this.initialize();
    }

    /**
     * Inicializa o sistema de auto-moderação
     */
    initialize() {
        this.client.logger.info('🤖 Sistema de Auto-Moderação inicializado');
        
        // Limpar cache e dados antigos a cada 10 minutos
        setInterval(() => {
            this.cleanupOldData();
        }, 600000);
    }

    /**
     * Processa uma mensagem para moderação automática
     */
    async processMessage(message) {
        // Ignorar bots e mensagens do sistema
        if (message.author.bot || message.system) return;

        // Ignorar DMs
        if (!message.guild) return;

        const guildId = message.guild.id;
        const userId = message.author.id;
        const content = message.content;

        try {
            // Verificar se auto-moderação está ativa
            const guildConfig = await this.client.database.getGuildConfig(guildId);
            if (!guildConfig || !guildConfig.auto_mod_enabled) {
                return;
            }

            // Verificar se o usuário tem permissões de moderador
            if (this.isModeratorOrAdmin(message.member)) {
                return;
            }

            // Verificar se o canal está na lista de ignorados
            if (this.isChannelIgnored(guildId, message.channel.id)) {
                return;
            }

            // Análises de conteúdo
            const violations = await this.analyzeMessage(message);
            
            if (violations.length > 0) {
                await this.handleViolations(message, violations);
            }

            // Rastreamento de spam/flood
            await this.trackUserActivity(message);

        } catch (error) {
            this.client.logger.error('Erro na auto-moderação:', error, {
                guild: guildId,
                user: userId,
                channel: message.channel.id
            });
        }
    }

    /**
     * Analisa uma mensagem em busca de violações
     */
    async analyzeMessage(message) {
        const violations = [];
        const content = message.content.toLowerCase();

        // 1. Filtro de palavrões
        if (this.filter.isProfane(content)) {
            violations.push({
                type: 'profanity',
                severity: 2,
                reason: 'Linguagem inapropriada detectada',
                action: 'delete'
            });
        }

        // 2. Filtro de spam (mensagens repetidas)
        const spamCheck = await this.checkSpam(message);
        if (spamCheck.isSpam) {
            violations.push({
                type: 'spam',
                severity: spamCheck.severity,
                reason: `Spam detectado: ${spamCheck.reason}`,
                action: spamCheck.severity >= 3 ? 'mute' : 'delete'
            });
        }

        // 3. Filtro de links
        const linkCheck = await this.checkLinks(message);
        if (linkCheck.violation) {
            violations.push({
                type: 'links',
                severity: linkCheck.severity,
                reason: linkCheck.reason,
                action: 'delete'
            });
        }

        // 4. Filtro de convites do Discord
        const inviteCheck = this.checkDiscordInvites(content);
        if (inviteCheck.violation) {
            violations.push({
                type: 'invites',
                severity: 2,
                reason: 'Convite não autorizado do Discord',
                action: 'delete'
            });
        }

        // 5. Filtro de menções em massa
        const mentionCheck = this.checkMassMentions(message);
        if (mentionCheck.violation) {
            violations.push({
                type: 'mass_mentions',
                severity: 2,
                reason: `Muitas menções: ${mentionCheck.count}`,
                action: 'delete'
            });
        }

        // 6. Filtro de CAPS excessivo
        const capsCheck = this.checkExcessiveCaps(content);
        if (capsCheck.violation) {
            violations.push({
                type: 'caps',
                severity: 1,
                reason: `Uso excessivo de maiúsculas: ${capsCheck.percentage}%`,
                action: 'warn'
            });
        }

        // 7. Análise de toxicidade com IA (se disponível)
        const toxicityCheck = await this.checkToxicity(content);
        if (toxicityCheck.violation) {
            violations.push({
                type: 'toxicity',
                severity: 3,
                reason: `Conteúdo tóxico detectado (${toxicityCheck.confidence}%)`,
                action: 'delete'
            });
        }

        // 8. Análise de sentimento
        const sentimentCheck = this.checkSentiment(content);
        if (sentimentCheck.violation) {
            violations.push({
                type: 'negative_sentiment',
                severity: 1,
                reason: `Sentimento muito negativo detectado`,
                action: 'log'
            });
        }

        return violations;
    }

    /**
     * Verifica spam e flood
     */
    async checkSpam(message) {
        const userId = message.author.id;
        const guildId = message.guild.id;
        const content = message.content;
        const now = Date.now();

        // Inicializar rastreamento do usuário
        const userKey = `${guildId}-${userId}`;
        if (!this.messageTracker.has(userKey)) {
            this.messageTracker.set(userKey, []);
        }

        const userMessages = this.messageTracker.get(userKey);
        userMessages.push({ content, timestamp: now, channelId: message.channel.id });

        // Remover mensagens antigas (últimos 30 segundos)
        const cutoff = now - 30000;
        const recentMessages = userMessages.filter(msg => msg.timestamp > cutoff);
        this.messageTracker.set(userKey, recentMessages);

        // Verificar spam por frequência
        if (recentMessages.length >= this.config.spamThreshold) {
            return {
                isSpam: true,
                severity: 3,
                reason: `${recentMessages.length} mensagens em 30 segundos`
            };
        }

        // Verificar flood (mensagens idênticas)
        const identicalMessages = recentMessages.filter(msg => 
            msg.content === content && msg.content.length > 5
        );
        
        if (identicalMessages.length >= this.config.floodThreshold) {
            return {
                isSpam: true,
                severity: 2,
                reason: `${identicalMessages.length} mensagens idênticas`
            };
        }

        return { isSpam: false };
    }

    /**
     * Verifica links suspeitos
     */
    async checkLinks(message) {
        const content = message.content;
        const urls = content.match(urlRegex()) || [];

        if (urls.length === 0) {
            return { violation: false };
        }

        try {
            // Verificar se links são permitidos no canal
            const guildConfig = await this.client.database.getGuildConfig(message.guild.id);
            let settings = {};

            if (guildConfig?.settings) {
                try {
                    if (typeof guildConfig.settings === 'string') {
                        settings = JSON.parse(guildConfig.settings);
                    } else {
                        settings = guildConfig.settings;
                    }
                } catch (parseError) {
                    console.error('Erro ao fazer parse das configurações de links:', parseError);
                    settings = {};
                }
            }

            if (settings.allowLinks === false) {
                return {
                    violation: true,
                    severity: 1,
                    reason: 'Links não são permitidos neste canal'
                };
            }

            // Verificar whitelist/blacklist
            for (const url of urls) {
                try {
                    const domain = this.extractDomain(url);

                    // Verificar blacklist
                    if (settings.blacklistedDomains?.includes(domain)) {
                        return {
                            violation: true,
                            severity: 3,
                            reason: `Domínio na lista negra: ${domain}`
                        };
                    }

                    // Verificar segurança do link (se API disponível)
                    const safetyCheck = await this.checkLinkSafety(url);
                    if (safetyCheck.unsafe) {
                        return {
                            violation: true,
                            severity: 3,
                            reason: `Link inseguro detectado: ${safetyCheck.reason}`
                        };
                    }
                } catch (urlError) {
                    console.error('Erro ao verificar URL:', urlError);
                    // Continuar com próxima URL em caso de erro
                    continue;
                }
            }

            return { violation: false };

        } catch (error) {
            console.error('Erro na verificação de links:', error);
            return { violation: false };
        }
    }

    /**
     * Verifica convites do Discord
     */
    checkDiscordInvites(content) {
        const inviteRegex = /(https?:\/\/)?(www\.)?(discord\.(gg|io|me|li)|discordapp\.com\/invite|discord\.com\/invite)\/[a-zA-Z0-9]+/gi;
        const matches = content.match(inviteRegex);

        if (matches && matches.length > 0) {
            return {
                violation: true,
                invites: matches
            };
        }

        return { violation: false };
    }

    /**
     * Verifica menções em massa
     */
    checkMassMentions(message) {
        const maxMentions = parseInt(process.env.MAX_MENTIONS) || 5;
        const totalMentions = message.mentions.users.size + message.mentions.roles.size;

        if (totalMentions > maxMentions) {
            return {
                violation: true,
                count: totalMentions
            };
        }

        return { violation: false };
    }

    /**
     * Verifica uso excessivo de maiúsculas
     */
    checkExcessiveCaps(content) {
        if (content.length < 10) return { violation: false };

        const capsCount = (content.match(/[A-Z]/g) || []).length;
        const percentage = (capsCount / content.length) * 100;

        if (percentage > 70) {
            return {
                violation: true,
                percentage: Math.round(percentage)
            };
        }

        return { violation: false };
    }

    /**
     * Verifica toxicidade usando IA
     */
    async checkToxicity(content) {
        // Verificar cache primeiro
        const contentHash = this.hashString(content);
        if (this.aiCache.has(contentHash)) {
            const cached = this.aiCache.get(contentHash);
            if (Date.now() - cached.timestamp < 3600000) { // 1 hora
                return cached.result;
            }
        }

        try {
            // Usar API Perspective do Google se disponível
            if (process.env.PERSPECTIVE_API_KEY) {
                const result = await this.analyzeToxicityWithPerspective(content);
                
                // Cache do resultado
                this.aiCache.set(contentHash, {
                    result: result,
                    timestamp: Date.now()
                });

                return result;
            }

            // Fallback: análise simples baseada em palavras-chave
            return this.analyzeToxicitySimple(content);

        } catch (error) {
            this.client.logger.error('Erro na análise de toxicidade:', error);
            return { violation: false };
        }
    }

    /**
     * Análise de toxicidade com Google Perspective API
     */
    async analyzeToxicityWithPerspective(content) {
        try {
            const response = await axios.post(
                `https://commentanalyzer.googleapis.com/v1alpha1/comments:analyze?key=${process.env.PERSPECTIVE_API_KEY}`,
                {
                    requestedAttributes: {
                        TOXICITY: {},
                        SEVERE_TOXICITY: {},
                        IDENTITY_ATTACK: {},
                        INSULT: {},
                        PROFANITY: {},
                        THREAT: {}
                    },
                    languages: ['pt', 'en'],
                    comment: { text: content }
                }
            );

            const scores = response.data.attributeScores;
            const toxicityScore = scores.TOXICITY?.summaryScore?.value || 0;
            const confidence = Math.round(toxicityScore * 100);

            if (toxicityScore > this.config.toxicityThreshold) {
                return {
                    violation: true,
                    confidence: confidence,
                    details: scores
                };
            }

            return { violation: false, confidence: confidence };

        } catch (error) {
            this.client.logger.error('Erro na API Perspective:', error);
            return { violation: false };
        }
    }

    /**
     * Análise simples de toxicidade
     */
    analyzeToxicitySimple(content) {
        const toxicWords = [
            'idiota', 'burro', 'estupido', 'imbecil', 'otario',
            // Adicionar mais palavras tóxicas em português
        ];

        const lowerContent = content.toLowerCase();
        const foundWords = toxicWords.filter(word => lowerContent.includes(word));

        if (foundWords.length >= 2) {
            return {
                violation: true,
                confidence: 80,
                reason: `Palavras tóxicas encontradas: ${foundWords.join(', ')}`
            };
        }

        return { violation: false };
    }

    /**
     * Verifica sentimento da mensagem
     */
    checkSentiment(content) {
        const analysis = this.sentiment.analyze(content);
        
        if (analysis.score <= -5 && analysis.comparative <= -0.8) {
            return {
                violation: true,
                score: analysis.score,
                comparative: analysis.comparative
            };
        }

        return { violation: false };
    }

    /**
     * Verifica segurança de links
     */
    async checkLinkSafety(url) {
        try {
            // Usar VirusTotal API se disponível
            if (process.env.VIRUSTOTAL_API_KEY) {
                // Implementar verificação com VirusTotal
                return { unsafe: false };
            }

            // Verificações básicas
            const domain = this.extractDomain(url);
            const suspiciousDomains = [
                'bit.ly', 'tinyurl.com', 'short.link',
                // Adicionar mais domínios suspeitos
            ];

            if (suspiciousDomains.includes(domain)) {
                return {
                    unsafe: true,
                    reason: 'Domínio encurtador suspeito'
                };
            }

            return { unsafe: false };

        } catch (error) {
            this.client.logger.error('Erro na verificação de segurança de link:', error);
            return { unsafe: false };
        }
    }

    /**
     * Manipula violações detectadas
     */
    async handleViolations(message, violations) {
        const guildId = message.guild.id;
        const userId = message.author.id;
        const highestSeverity = Math.max(...violations.map(v => v.severity));

        try {
            // Determinar ação baseada na severidade
            let action = 'warn';
            if (violations.some(v => v.action === 'mute')) action = 'mute';
            if (violations.some(v => v.action === 'delete')) action = 'delete';

            // Executar ação
            switch (action) {
                case 'delete':
                    await message.delete().catch(() => {});
                    await this.sendModerationMessage(message, violations, 'delete');
                    break;

                case 'mute':
                    await this.muteUser(message.member, violations);
                    await message.delete().catch(() => {});
                    break;

                case 'warn':
                    await this.warnUser(message.member, violations);
                    break;
            }

            // Registrar no banco de dados
            for (const violation of violations) {
                this.client.database.logModerationAction(
                    guildId, userId, null, `automod_${violation.type}`, 
                    violation.reason, null, message.channel.id, message.id,
                    { automatic: true, severity: violation.severity, action: action }
                );
            }

            // Log da ação
            this.client.logger.moderation(
                `AutoMod: ${action}`, guildId, userId, null,
                violations.map(v => v.reason).join(', '),
                { violations: violations.length, severity: highestSeverity }
            );

        } catch (error) {
            this.client.logger.error('Erro ao lidar com violações:', error);
        }
    }

    /**
     * Envia mensagem de moderação
     */
    async sendModerationMessage(message, violations, action) {
        const embed = new EmbedBuilder()
            .setColor('#e74c3c')
            .setTitle('🚨 Moderação Automática')
            .setDescription(`${message.author}, sua mensagem foi ${action === 'delete' ? 'removida' : 'moderada'} automaticamente.`)
            .addFields({
                name: 'Motivo',
                value: violations.map(v => `• ${v.reason}`).join('\n'),
                inline: false
            })
            .setTimestamp()
            .setFooter({ text: 'Nova Moderação Bot' });

        const reply = await message.channel.send({ embeds: [embed] }).catch(() => null);
        
        // Remover mensagem após 10 segundos
        if (reply) {
            setTimeout(() => {
                reply.delete().catch(() => {});
            }, 10000);
        }
    }

    /**
     * Silencia um usuário
     */
    async muteUser(member, violations) {
        // Implementar sistema de mute
        this.client.logger.info(`Usuário ${member.user.tag} silenciado automaticamente`);
    }

    /**
     * Adverte um usuário
     */
    async warnUser(member, violations) {
        const reason = violations.map(v => v.reason).join(', ');
        
        // Adicionar advertência no banco
        this.client.database.addWarning(
            member.guild.id, member.user.id, null, 
            `AutoMod: ${reason}`, 1
        );

        this.client.logger.info(`Usuário ${member.user.tag} advertido automaticamente`);
    }

    /**
     * Utilitários
     */
    isModeratorOrAdmin(member) {
        return member.permissions.has(['ModerateMembers', 'ManageMessages', 'Administrator']);
    }

    isChannelIgnored(guildId, channelId) {
        const guildConfig = this.client.database.getGuildConfig(guildId);
        const ignoredChannels = JSON.parse(guildConfig?.ignored_channels || '[]');
        return ignoredChannels.includes(channelId);
    }

    extractDomain(url) {
        try {
            return new URL(url).hostname;
        } catch {
            return '';
        }
    }

    hashString(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
        }
        return hash.toString();
    }

    trackUserActivity(message) {
        // Implementar rastreamento de atividade do usuário
    }

    cleanupOldData() {
        const now = Date.now();
        const cutoff = now - 1800000; // 30 minutos

        // Limpar cache de IA
        for (const [key, data] of this.aiCache.entries()) {
            if (data.timestamp < cutoff) {
                this.aiCache.delete(key);
            }
        }

        // Limpar rastreamento de mensagens
        for (const [key, messages] of this.messageTracker.entries()) {
            const recentMessages = messages.filter(msg => msg.timestamp > cutoff);
            if (recentMessages.length === 0) {
                this.messageTracker.delete(key);
            } else {
                this.messageTracker.set(key, recentMessages);
            }
        }

        this.client.logger.debug('Limpeza de dados da auto-moderação concluída');
    }

    /**
     * Obtém estatísticas do sistema
     */
    getStats() {
        return {
            cachedAnalyses: this.aiCache.size,
            trackedUsers: this.messageTracker.size,
            config: this.config
        };
    }
}

module.exports = AutoModeration;
